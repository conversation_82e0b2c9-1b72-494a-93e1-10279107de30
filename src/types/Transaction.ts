export enum TransactionType {
  TRANSFER = 'transfer',
  SPEND = 'spend',
  RETURNED = 'return'
}

export enum TransactionStatus {
  DRAFT = 'draft',
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected'
}

export interface TransactionUser {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  school_class?: string;
}

export interface TransactionCategory {
  code: string;
  name: string;
  type: 'GL' | 'Program';
  parent: string;
}

export interface Transaction {
  id: string;
  type: TransactionType;
  status: TransactionStatus;
  amount: number;
  currency: string;
  description?: string;
  purpose?: string;
  glCode: string;
  programCode: string;
  receiptUri?: string;
  createdBy: string;
  recipient?: string; // for cash_given
  splitWith?: string[]; // user IDs
  createdAt: Date;
  updatedAt: Date;
  approvedBy?: string;
  rejectionReason?: string;
  // Additional fields from API
  user?: TransactionUser;
  category?: TransactionCategory;
}
