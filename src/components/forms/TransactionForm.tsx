import React, { useState } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { useSelector } from 'react-redux';
import { TransactionType } from '../../types/Transaction';
import { UserRole } from '../../types/User';
import { transactionSchema } from '../../utils/validation';
import { RootState } from '../../store/store';
import Input from '../common/Input';
import Button from '../common/Button';
import ImagePicker from './ImagePicker';
import CategoryPicker from './CategoryPicker';
import StudentSplitPicker from './StudentSplitPicker';
import RecipientPicker from './RecipientPicker';
import { DropdownProvider } from '../../contexts/DropdownContext';

interface TransactionFormData {
  amount: string;
  description?: string;
  purpose?: string;
  glCode: string;
  programCode: string;
  receiptUri?: string;
  recipient?: string;
  splitWith?: string[];
  splitWithText?: string; // Pre-formatted text for split with display
}

interface TransactionFormProps {
  type: TransactionType;
  onSubmit: (data: TransactionFormData) => void;
  onSaveDraft: (data: TransactionFormData) => void;
  initialData?: Partial<TransactionFormData>;
  loading?: boolean;
}

export default function TransactionForm({
  type,
  onSubmit,
  onSaveDraft,
  initialData,
  loading = false
}: TransactionFormProps) {
  const { user } = useSelector((state: RootState) => state.auth);
  const [splitWithText, setSplitWithText] = useState<string>('');
  const { control, handleSubmit, formState: { errors, isValid } } = useForm<TransactionFormData>({
    resolver: yupResolver(transactionSchema(type)),
    defaultValues: {
      splitWith: [],
      ...initialData,
    },
    mode: 'onChange',
  });

  // Check if current user is a student
  const isStudent = user?.role === UserRole.STUDENT;

  // Wrapper functions to include splitWithText in form data
  const handleFormSubmit = (data: TransactionFormData) => {
    onSubmit({ ...data, splitWithText });
  };

  const handleFormSaveDraft = (data: TransactionFormData) => {
    onSaveDraft({ ...data, splitWithText });
  };

  return (
    <DropdownProvider>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        <View style={styles.form}>
        <Controller
          control={control}
          name="amount"
          render={({ field: { onChange, value } }) => (
            <Input
              label="Amount"
              value={value}
              onChangeText={onChange}
              keyboardType="numeric"
              placeholder="0.00"
              error={errors.amount?.message}
              required
            />
          )}
        />

        {type === TransactionType.TRANSFER && (
          <RecipientPicker
            control={control}
            name="recipient"
            error={errors.recipient?.message}
          />
        )}

        {type === TransactionType.TRANSFER && (
          <Controller
            control={control}
            name="purpose"
            render={({ field: { onChange, value } }) => (
              <Input
                label="Purpose"
                value={value}
                onChangeText={onChange}
                placeholder="Purpose of cash transfer"
                multiline
                numberOfLines={3}
                error={errors.purpose?.message}
              />
            )}
          />
        )}

        {(type === TransactionType.SPEND || type === TransactionType.RETURNED) && (
          <Controller
            control={control}
            name="description"
            render={({ field: { onChange, value } }) => (
              <Input
                label="Description"
                value={value}
                onChangeText={onChange}
                placeholder="What was this expense for?"
                multiline
                numberOfLines={3}
                error={errors.description?.message}
                required
              />
            )}
          />
        )}

        <CategoryPicker
          control={control}
          name="glCode"
          label="GL Code"
          type="gl"
          error={errors.glCode?.message}
        />

        <CategoryPicker
          control={control}
          name="programCode"
          label="Program Code"
          type="program"
          error={errors.programCode?.message}
        />

        <StudentSplitPicker
            control={control}
            name="splitWith"
            error={errors.splitWith?.message}
            onDisplayTextChange={setSplitWithText}
        />

        <ImagePicker
          control={control}
          name="receiptUri"
          // required={type === TransactionType.SPENT || type === TransactionType.RETURNED}
          label="Receipt"
        />

        <View style={styles.buttonContainer}>
          <Button
            title="Save Draft"
            onPress={handleSubmit(handleFormSaveDraft)}
            variant="outline"
            style={styles.button}
            loading={loading}
          />
          <Button
            title="Submit"
            onPress={handleSubmit(handleFormSubmit)}
            variant="primary"
            style={styles.button}
            disabled={!isValid}
            loading={loading}
          />
        </View>
        </View>
      </ScrollView>
    </DropdownProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  form: {
    padding: 16,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
    gap: 12,
  },
  button: {
    flex: 1,
  },
});
