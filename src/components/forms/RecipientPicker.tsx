import React from 'react';
import { User, UserRole } from '../../types/User';
import { apiService } from '../../services/apiService';
import { mockUsers } from '../../store/mockData/users';
import UserPicker from './UserPicker';

interface RecipientPickerProps {
  control: any;
  name: string;
  error?: string;
}

export default function RecipientPicker({
  control,
  name,
  error,
}: RecipientPickerProps) {

  // Fetch all users for recipient selection using the new API
  const fetchAllUsers = async (): Promise<User[]> => {
    try {
      const fetchedUsers = await apiService.getUsers(false); // only_student=false (all users)
      return fetchedUsers;
    } catch (error) {
      console.error('Failed to fetch all users:', error);
      // Fallback to mock data if API fails
      return mockUsers;
    }
  };

  // Search users function for backend search
  const searchAllUsers = async (query: string): Promise<User[]> => {
    try {
      const searchResults = await apiService.searchUsers(query, false); // only_student=false (all users)
      return searchResults;
    } catch (error) {
      console.error('Failed to search users:', error);
      // Fallback to empty results if search fails
      return [];
    }
  };

  return (
    <UserPicker
      control={control}
      name={name}
      label="Recipient"
      placeholder="Select recipient"
      error={error}
      multiplePick={false}
      fetchUsers={fetchAllUsers}
      searchUsers={searchAllUsers}
      required={true}
    />
  );
}
