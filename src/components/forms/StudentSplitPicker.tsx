import React from 'react';
import { User, UserRole } from '../../types/User';
import { apiService } from '../../services/apiService';
import { mockUsers } from '../../store/mockData/users';
import UserPicker from './UserPicker';

interface StudentSplitPickerProps {
  control: any;
  name: string;
  error?: string;
  onDisplayTextChange?: (text: string) => void; // Callback to provide formatted text
}

export default function StudentSplitPicker({
  control,
  name,
  error,
  onDisplayTextChange,
}: StudentSplitPickerProps) {

  // Fetch students for split selection using the new API
  const fetchStudents = async (): Promise<User[]> => {
    try {
      const fetchedStudents = await apiService.getUsers(true); // only_student=true
      return fetchedStudents;
    } catch (error) {
      console.error('Failed to fetch students:', error);
      // Fallback to mock data if API fails
      const fallbackStudents = mockUsers.filter(user => user.role === UserRole.STUDENT);
      return fallbackStudents;
    }
  };

  // Search students function for backend search
  const searchStudents = async (query: string): Promise<User[]> => {
    try {
      const searchResults = await apiService.searchUsers(query, true); // only_student=true
      return searchResults;
    } catch (error) {
      console.error('Failed to search students:', error);
      // Fallback to empty results if search fails
      return [];
    }
  };

  return (
    <UserPicker
      control={control}
      name={name}
      label="Split with other students (Equal split only)"
      placeholder="Select students to split with"
      error={error}
      multiplePick={true}
      fetchUsers={fetchStudents}
      searchUsers={searchStudents}
      required={false}
      onDisplayTextChange={onDisplayTextChange}
    />
  );
}
