import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { format } from 'date-fns';
import { Transaction, TransactionType } from '../../types/Transaction';
import { Colors } from '../../constants/colors';
import { formatCurrency } from '../../utils/currency';
import StatusBadge from '../common/StatusBadge';

interface ApprovalCardProps {
  transaction: Transaction;
  onApprove: (transactionId: string) => void;
  onReject: (transactionId: string) => void;
  onViewReceipt?: (transactionId: string) => void;
  loading?: boolean;
}

export default function ApprovalCard({
  transaction,
  onApprove,
  onReject,
  onViewReceipt,
  loading = false
}: ApprovalCardProps) {

  const getTransactionTypeDisplay = (type: TransactionType) => {
    switch (type) {
      case TransactionType.TRANSFER:
        return 'Cash Given';
      case TransactionType.SPEND:
        return 'Cash Spent';
      case TransactionType.RETURNED:
        return 'Cash Returned';
      default:
        return 'Transaction';
    }
  };

  const getUserDisplayName = () => {
    if (!transaction.user) return 'Unknown User';

    const { first_name, last_name, email } = transaction.user;

    if (first_name && last_name) {
      return `${first_name} ${last_name}`;
    }

    if (first_name) {
      return first_name;
    }

    return email.split('@')[0];
  };

  const getUserAvatar = () => {
    if (!transaction.user) return 'U';

    const { first_name, email } = transaction.user;

    if (first_name) {
      return first_name.charAt(0).toUpperCase();
    }

    return email.charAt(0).toUpperCase();
  };

  return (
    <View style={styles.card}>
      {/* Header with user avatar, name, amount and status */}
      <View style={styles.header}>
        <View style={styles.userAvatar}>
          <Text style={styles.avatarText}>{getUserAvatar()}</Text>
        </View>
        <View style={styles.userInfo}>
          <Text style={styles.userName}>{getUserDisplayName()}</Text>
          <Text style={styles.transactionType}>
            {getTransactionTypeDisplay(transaction.type)} • {format(new Date(transaction.createdAt), 'MMM dd, yyyy')}
          </Text>
        </View>
        <View style={styles.rightSection}>
          <Text style={styles.amount}>
            {formatCurrency(transaction.amount, transaction.currency)}
          </Text>
          <StatusBadge status={transaction.status} size="small" />
        </View>
      </View>

      {/* Transaction description */}
      <View style={styles.descriptionSection}>
        <Text style={styles.description} numberOfLines={2}>
          {transaction.description || 'No description provided'}
        </Text>
      </View>

      {/* GL Code and Program Code */}
      <View style={styles.codesSection}>
        <Text style={styles.codeText}>
          GL Code: <Text style={styles.codeValue}>{transaction.category?.parent || transaction.glCode}</Text>
        </Text>
        <Text style={styles.codeText}>
          Program: <Text style={styles.codeValue}>{transaction.category?.code || transaction.programCode}</Text>
        </Text>
      </View>

      {/* Action buttons */}
      <View style={styles.actionButtons}>
        {transaction.receiptUri && (
          <TouchableOpacity
            style={[styles.actionButton, styles.viewReceiptButton]}
            onPress={() => onViewReceipt?.(transaction.id)}
          >
            <Text style={styles.viewReceiptText}>📄 View Receipt</Text>
          </TouchableOpacity>
        )}
        <TouchableOpacity
          style={[styles.actionButton, styles.rejectButton]}
          onPress={() => onReject(transaction.id)}
          disabled={loading}
        >
          <Text style={styles.rejectText}>✕ Reject</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.actionButton, styles.approveButton]}
          onPress={() => onApprove(transaction.id)}
          disabled={loading}
        >
          <Text style={styles.approveText}>✓ Approve</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: Colors.background,
    borderRadius: 12,
    padding: 20,
    marginVertical: 6,
    marginHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  userAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: Colors.gray200,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  avatarText: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: 4,
  },
  transactionType: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  rightSection: {
    alignItems: 'flex-end',
  },
  amount: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.textPrimary,
    marginBottom: 4,
  },
  descriptionSection: {
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    color: Colors.textPrimary,
    lineHeight: 22,
  },
  codesSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  codeText: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  codeValue: {
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  viewReceiptButton: {
    backgroundColor: Colors.gray100,
  },
  viewReceiptText: {
    fontSize: 14,
    color: Colors.textSecondary,
    fontWeight: '500',
  },
  rejectButton: {
    backgroundColor: Colors.gray100,
  },
  rejectText: {
    fontSize: 14,
    color: Colors.error,
    fontWeight: '500',
  },
  approveButton: {
    backgroundColor: Colors.success,
  },
  approveText: {
    fontSize: 14,
    color: Colors.background,
    fontWeight: '500',
  },
});
