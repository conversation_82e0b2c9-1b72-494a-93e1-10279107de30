import { Transaction, TransactionType, TransactionStatus } from '../../types/Transaction';

export const mockTransactions: Transaction[] = [
  {
    id: '1',
    type: TransactionType.SPEND,
    status: TransactionStatus.APPROVED,
    amount: 45.50,
    currency: 'USD',
    description: 'Lunch at local restaurant',
    glCode: 'GL001',
    programCode: 'PR001',
    receiptUri: 'https://via.placeholder.com/300x400',
    createdBy: '1',
    createdAt: new Date('2025-01-15T12:30:00Z'),
    updatedAt: new Date('2025-01-15T12:30:00Z')
  },
  {
    id: '2',
    type: TransactionType.TRANSFER,
    status: TransactionStatus.APPROVED,
    amount: 100.00,
    currency: 'USD',
    description: 'Field trip expenses',
    purpose: 'Weekly allowance',
    glCode: 'GL004',
    programCode: 'PR001',
    createdBy: '2',
    recipient: '<PERSON>',
    createdAt: new Date('2025-01-14T09:00:00Z'),
    updatedAt: new Date('2025-01-14T09:15:00Z'),
    approvedBy: '2'
  },
  {
    id: '3',
    type: TransactionType.SPEND,
    status: TransactionStatus.PENDING,
    amount: 23.75,
    currency: 'USD',
    description: 'Transportation costs',
    glCode: 'GL002',
    programCode: 'PR001',
    receiptUri: 'https://via.placeholder.com/300x400',
    createdBy: '1',
    createdAt: new Date('2025-01-13T14:20:00Z'),
    updatedAt: new Date('2025-01-13T16:45:00Z')
  },
  {
    id: '4',
    type: TransactionType.SPEND,
    status: TransactionStatus.DRAFT,
    amount: 67.20,
    currency: 'USD',
    description: 'Museum entrance fees',
    glCode: 'GL005',
    programCode: 'PR001',
    receiptUri: 'https://via.placeholder.com/300x400',
    createdBy: '1',
    createdAt: new Date('2025-01-12T16:00:00Z'),
    updatedAt: new Date('2025-01-12T16:00:00Z')
  },
  {
    id: '5',
    type: TransactionType.TRANSFER,
    status: TransactionStatus.REJECTED,
    amount: 50.00,
    currency: 'USD',
    description: 'Emergency funds',
    glCode: 'GL001',
    programCode: 'PR001',
    createdBy: '2',
    recipient: 'Alex Chen',
    createdAt: new Date('2025-01-11T18:30:00Z'),
    updatedAt: new Date('2025-01-11T18:30:00Z')
  }
];
