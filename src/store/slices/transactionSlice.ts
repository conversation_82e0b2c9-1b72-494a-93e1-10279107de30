import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Transaction, TransactionStatus } from '../../types/Transaction';

interface TransactionState {
  transactions: Transaction[];
  loading: boolean;
  selectedTransaction: Transaction | null;
  error: string | null;
}

const initialState: TransactionState = {
  transactions: [],
  loading: false,
  selectedTransaction: null,
  error: null,
};

const transactionSlice = createSlice({
  name: 'transactions',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    addTransaction: (state, action: PayloadAction<Transaction>) => {
      state.transactions.unshift(action.payload);
    },
    updateTransaction: (state, action: PayloadAction<Transaction>) => {
      const index = state.transactions.findIndex(t => t.id === action.payload.id);
      if (index !== -1) {
        state.transactions[index] = action.payload;
      }
    },
    approveTransaction: (state, action: PayloadAction<{ id: string; approvedBy: string }>) => {
      const transaction = state.transactions.find(t => t.id === action.payload.id);
      if (transaction) {
        transaction.status = TransactionStatus.APPROVED;
        transaction.approvedBy = action.payload.approvedBy;
        transaction.updatedAt = new Date();
      }
    },
    rejectTransaction: (state, action: PayloadAction<{ id: string; reason: string }>) => {
      const transaction = state.transactions.find(t => t.id === action.payload.id);
      if (transaction) {
        transaction.status = TransactionStatus.REJECTED;
        transaction.rejectionReason = action.payload.reason;
        transaction.updatedAt = new Date();
      }
    },
    setSelectedTransaction: (state, action: PayloadAction<Transaction | null>) => {
      state.selectedTransaction = action.payload;
    },
    deleteTransaction: (state, action: PayloadAction<string>) => {
      state.transactions = state.transactions.filter(t => t.id !== action.payload);
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const { 
  setLoading,
  addTransaction, 
  updateTransaction, 
  approveTransaction, 
  rejectTransaction,
  setSelectedTransaction,
  deleteTransaction,
  setError
} = transactionSlice.actions;
export default transactionSlice.reducer;
