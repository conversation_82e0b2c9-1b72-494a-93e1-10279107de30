import { GoogleSignin, statusCodes } from '@react-native-google-signin/google-signin';
import { Platform } from 'react-native';
import { config, debugLog } from '../config/env';
import { apiService } from './apiService';
import { storageService, StoredUserData } from './storageService';
import { firebaseService } from './firebaseService';

// Google OAuth configuration
const GOOGLE_CONFIG = {
  iosClientId: config.googleIosClientId,
};

export interface GoogleUser {
  id: string;
  email: string;
  name: string;
  photo?: string;
  givenName?: string;
  familyName?: string;
}

export interface GoogleAuthResult {
  user: any; // User from our backend
  googleUser: GoogleUser;
  tokens: {
    accessToken: string;
    refreshToken: string;
  };
}

export class GoogleAuthService {
  private static instance: GoogleAuthService;
  private isConfigured = false;

  public static getInstance(): GoogleAuthService {
    if (!GoogleAuthService.instance) {
      GoogleAuthService.instance = new GoogleAuthService();
    }
    return GoogleAuthService.instance;
  }

  private async configure(): Promise<void> {
    if (this.isConfigured) return;

    try {
      await GoogleSignin.configure({
        iosClientId: GOOGLE_CONFIG.iosClientId,
        offlineAccess: false,
        // hostedDomain: 'thinkglobalschool.com', // Restrict to TGS domain
        forceCodeForRefreshToken: true,
        accountName: '',
        googleServicePlistPath: '',
        openIdRealm: '',
        profileImageSize: 120,
      });
      this.isConfigured = true;
    } catch (error) {
      console.error('Google Sign-In configuration error:', error);
      // For demo purposes, we'll continue without throwing
    }
  }

  async signIn(): Promise<GoogleAuthResult | null> {
    try {
      await this.configure();
      debugLog('Starting Google Sign-In process');

      let googleUser: GoogleUser;
      let googleTokens: any = {};

      try {
        // Check if device supports Google Play Services (Android)
        await GoogleSignin.hasPlayServices();

        // Attempt to sign in
        const userInfo = await GoogleSignin.signIn();

        // // Validate domain
        // if (!userInfo.data?.user?.email?.endsWith('@thinkglobalschool.com')) {
        //   await GoogleSignin.signOut();
        //   throw new Error('Please use a Think Global School email address');
        // }

        googleUser = {
          id: userInfo.data.user.id,
          email: userInfo.data.user.email,
          name: userInfo.data.user.name || '',
          photo: userInfo.data.user.photo || undefined,
          givenName: userInfo.data.user.givenName || undefined,
          familyName: userInfo.data.user.familyName || undefined,
        };

        googleTokens = {
          accessToken: userInfo.data.idToken,
          idToken: userInfo.data.idToken,
        };

        if (!googleTokens.idToken) {
          throw new Error('Failed to get Google ID token');
        }

      } catch (googleError: any) {
        // If Google Sign-In fails in development, fall back to mock data
        if (__DEV__ && (googleError.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE ||
                        googleError.message?.includes('DEVELOPER_ERROR'))) {
          debugLog('Google Sign-In not available, using mock data for development');
          googleUser = await this.mockGoogleLogin();
          googleTokens = {
            accessToken: 'mock-id-token-for-dev',
            idToken: 'mock-id-token-for-dev',
          };
        } else {
          throw googleError;
        }
      }

      // Exchange Google ID token for Firebase ID token
      debugLog('Exchanging Google token for Firebase token');
      const firebaseResult = await firebaseService.exchangeGoogleTokenForFirebaseToken(googleTokens.idToken);

      // Authenticate with backend using Firebase ID token
      debugLog('Authenticating with backend API using Firebase ID token');
      const authResponse = await apiService.authenticateWithGoogle(googleUser, {
        idToken: firebaseResult.firebaseIdToken,
      });

      // Store tokens
      const tokens = {
        accessToken: authResponse.access,
        refreshToken: authResponse.refresh,
      };

      await storageService.storeTokens(tokens);

      // Create user object from Google user info
      // Since the backend doesn't return user info, we'll use Google user data
      const isStaff = googleUser.email.includes('advisor') || googleUser.email.includes('staff') || googleUser.email.includes('appdev')
      const user = {
        id: googleUser.id,
        email: googleUser.email,
        first_name: googleUser.givenName || googleUser.name.split(' ')[0] || '',
        last_name: googleUser.familyName || googleUser.name.split(' ').slice(1).join(' ') || '',
        name: googleUser.name,
        role: isStaff ? 'staff' as any : 'student' as any, // Basic role detection
        isAdvisor: isStaff,
        assignedStudents: [], // Will be populated later if needed
      };

      const storedUserData: StoredUserData = {
        user,
        googleUser,
        tokens,
        lastLogin: new Date().toISOString(),
      };

      await storageService.storeUserData(storedUserData);
      debugLog('User data stored successfully');

      return {
        user,
        googleUser,
        tokens,
      };

    } catch (error: any) {
      debugLog('Google Sign-In Error:', error);

      if (error.code === statusCodes.SIGN_IN_CANCELLED) {
        return null; // User cancelled
      } else if (error.code === statusCodes.IN_PROGRESS) {
        throw new Error('Sign in is already in progress');
      } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
        throw new Error('Google Play Services not available');
      } else {
        throw error;
      }
    }
  }

  private async mockGoogleLogin(): Promise<GoogleUser> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Mock Google user data - in production this would come from Google
    const mockGoogleUsers = [
      {
        id: 'google_1',
        email: '<EMAIL>',
        name: 'John Student',
        photo: 'https://via.placeholder.com/150',
        givenName: 'John',
        familyName: 'Student',
      },
      {
        id: 'google_2',
        email: '<EMAIL>',
        name: 'Jane Advisor',
        photo: 'https://via.placeholder.com/150',
        givenName: 'Jane',
        familyName: 'Advisor',
      },
      {
        id: 'google_3',
        email: '<EMAIL>',
        name: 'Admin User',
        photo: 'https://via.placeholder.com/150',
        givenName: 'Admin',
        familyName: 'User',
      },
    ];

    // For demo, randomly select a user or use the first one
    return mockGoogleUsers[0];
  }

  async signOut(): Promise<void> {
    try {
      debugLog('Starting sign out process');
      await this.configure();

      // Note: Firebase sign out removed for now due to initialization issues

      // Sign out from Google
      try {
        await GoogleSignin.signOut();
        debugLog('Google sign out successful');
      } catch (signOutError) {
        debugLog('Google sign out error (continuing anyway):', signOutError);
      }

      // Clear all stored data
      await storageService.clearAllData();
      debugLog('All user data cleared');

    } catch (error) {
      debugLog('Sign out error:', error);
      // Still clear local data even if Google sign out fails
      await storageService.clearAllData();
    }
  }

  async getCurrentUser(): Promise<GoogleUser | null> {
    try {
      await this.configure();

      const userInfo = GoogleSignin.getCurrentUser();
      if (!userInfo?.user) return null;

      return {
        id: userInfo.user.id,
        email: userInfo.user.email,
        name: userInfo.user.name || '',
        photo: userInfo.user.photo || undefined,
        givenName: userInfo.user.givenName || undefined,
        familyName: userInfo.user.familyName || undefined,
      };
    } catch (error) {
      console.error('Get current user error:', error);
      return null;
    }
  }

  async revokeAccess(): Promise<void> {
    try {
      await this.configure();
      await GoogleSignin.revokeAccess();
      await storageService.clearAllData();
      debugLog('Access revoked and data cleared');
    } catch (error) {
      debugLog('Revoke access error:', error);
      await storageService.clearAllData();
    }
  }

  // Restore user session from storage
  async restoreSession(): Promise<GoogleAuthResult | null> {
    try {
      debugLog('Attempting to restore user session');

      const userData = await storageService.getUserData();
      if (!userData) {
        debugLog('No stored user data found');
        return null;
      }

      // Check if tokens are still valid by trying to refresh
      const isValidToken = await apiService.ensureValidToken();
      if (!isValidToken) {
        debugLog('Tokens are invalid, clearing stored data');
        await storageService.clearAllData();
        return null;
      }

      // Get updated tokens
      const tokens = await storageService.getTokens();
      if (!tokens) {
        return null;
      }

      debugLog('Session restored successfully');
      return {
        user: userData.user,
        googleUser: userData.googleUser,
        tokens,
      };
    } catch (error) {
      debugLog('Session restore error:', error);
      await storageService.clearAllData();
      return null;
    }
  }
}

export const googleAuthService = GoogleAuthService.getInstance();
